server-name: xbit-agent
zap:
  level: info
  format: console
  prefix: "[gitlab.ggwp.life/xbit/xbit-dex/xbit-agent]"
  director: log
  show-line: true
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  log-in-console: true
  retention-day: -1

# redis configuration
redis:
  useCluster: false
  addr: {{ index . "REDIS_HOST" }}:{{ index . "REDIS_PORT" }}
  password: {{ index . "REDIS_PASS" }}
  db: 0

# system configuration
system:
  env: local
  addr: 9001
  db-type: pgsql
  use-redis: true
  use-mongo: false
  use-multipoint: false
  iplimit-count: 15000
  iplimit-time: 360
  router-prefix: "/api/agent"

# pgsql connect configuration
pgsql:
  path: {{ index . "POSTGRES_HOST" }}
  port: {{ index . "POSTGRES_PORT" }}
  config: ""
  db-name: "xbit_agent"
  username: {{ index . "POSTGRES_USER" }}
  password: {{ index . "POSTGRES_PASS" }}
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false

# nats configuration
nats:
  url: {{ index . "NATS_URL" }}
  token: {{ index . "NATS_AUTH_TOKEN" }}

# agent configuration
agent:
  # Rebate agent specific configs
  rebate:
    enabled: true
    check-interval: "5m"
    max-levels: 5
    commission-rates:
      level1: 0.1  # 10%
      level2: 0.05 # 5%
      level3: 0.03 # 3%
      level4: 0.02 # 2%
      level5: 0.01 # 1%

  # Trading agent configs
  trading:
    enabled: true
    risk-management:
      max-position-size: 1000
      stop-loss-percentage: 0.05
      take-profit-percentage: 0.1

  # Notification agent configs
  notification:
    enabled: true
    channels:
      - email
      - telegram
      - webhook

# temporal configuration
temporal:
  hostport: "xbit-temporal:7233"
  namespace: "xbit-agent"

# cron-tasks configuration
cron-tasks:
  - id: "process_rebates"
    cron: "*/5 * * * *"
  - id: "sync_user_hierarchy"
    cron: "0 */10 * * * *"
  - id: "calculate_commissions"
    cron: "0 0 * * * *"
  - id: "send_notifications"
    cron: "*/1 * * * * *"

# cors configuration
cors:
  mode: allow-all
  whitelist:
    - allow-origin: "*"
      allow-headers: Content-Type,AccessToken,X-CSRF-Token,Authorization,Token,X-Token,X-User-Id
      allow-methods: POST,GET,PUT,DELETE,OPTIONS
      expose-headers: Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type
      allow-credentials: true