package model

import (
	"time"

	"github.com/google/uuid"
)

type Trade struct {
	BaseModel
	UserID   uuid.UUID `gorm:"column:user_id;type:uuid;not null"`
	Symbol   string    `gorm:"column:symbol;size:50;not null"`
	Side     string    `gorm:"column:side;size:10;not null"` // buy/sell
	Amount   float64   `gorm:"column:amount;not null"`
	Price    float64   `gorm:"column:price;not null"`
	Volume   float64   `gorm:"column:volume;not null"`
	Fee      float64   `gorm:"column:fee;not null"`
	Status   string    `gorm:"column:status;size:20;default:'completed'"`
	TradedAt time.Time `gorm:"column:traded_at;not null"`

	// Relationships
	User User `gorm:"foreignKey:UserID"`
}

func (Trade) TableName() string {
	return "trades"
}
