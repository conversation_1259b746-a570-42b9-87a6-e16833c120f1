# Where are all the schema files located? globs are supported eg  src/**/*.graphqls
schema:
  - internal/controller/graphql/*.graphqls
  - internal/controller/graphql/schemas/*.gql

# Where should the generated server code go?
exec:
  filename: internal/controller/graphql/generated.go
  package: graphql

# Where should any generated models go?
model:
  filename: internal/controller/graphql/gql_model/models_gen.go
  package: gql_model

directives:
  auth:
    implementation: AuthDirective

# Where should the resolver implementations go?
resolver:
  layout: follow-schema
  dir: internal/controller/graphql/
  package: graphql
  filename_template: "{name}.resolvers.go"

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
  - "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"

# This section declares type mapping between the GraphQL and go type systems
models:
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Time:
    model:
      - github.com/99designs/gqlgen/graphql.Time