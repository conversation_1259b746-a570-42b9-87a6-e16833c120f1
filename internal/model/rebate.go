package model

import (
	"time"

	"github.com/google/uuid"
)

type Rebate struct {
	BaseModel
	UserID      uuid.UUID  `gorm:"column:user_id;type:uuid;not null"`
	ReferrerID  uuid.UUID  `gorm:"column:referrer_id;type:uuid;not null"`
	Level       int        `gorm:"column:level;not null"`
	Amount      float64    `gorm:"column:amount;not null"`
	Commission  float64    `gorm:"column:commission;not null"`
	TradeVolume float64    `gorm:"column:trade_volume;not null"`
	Status      string     `gorm:"column:status;size:20;default:'pending'"`
	ProcessedAt *time.Time `gorm:"column:processed_at"`

	// Relationships
	User     User `gorm:"foreignKey:UserID"`
	Referrer User `gorm:"foreignKey:ReferrerID"`
}

func (Rebate) TableName() string {
	return "rebates"
}
