erDiagram
    USERS {
        uuid id PK "Primary Key"
        varchar username UK "Unique username"
        varchar email UK "Unique email"
        varchar wallet_address UK "Crypto wallet address"
        varchar referral_code UK "Unique referral code"
        uuid parent_id FK "Self-reference to users.id"
        integer level "Hierarchy level (0-5)"
        varchar status "active, inactive, suspended"
        decimal total_volume "Total trading volume"
        decimal total_rebate "Total rebate earned"
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at "Soft delete"
    }

    HIERARCHIES {
        uuid id PK "Primary Key"
        uuid user_id FK "Reference to users.id"
        uuid ancestor_id FK "Reference to users.id (ancestor)"
        integer level "Distance from ancestor (0-5)"
        varchar path "Path string from root to node"
        boolean is_direct "Direct parent-child relationship"
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at "Soft delete"
    }

    TRADES {
        uuid id PK "Primary Key"
        uuid user_id FK "Reference to users.id"
        varchar symbol "Trading pair (BTC/USDT, ETH/USDT)"
        varchar side "BUY or SELL"
        decimal amount "Trade amount"
        decimal price "Execution price"
        decimal volume "Total volume (amount * price)"
        decimal fee "Trading fee"
        varchar status "completed, pending, failed, cancelled"
        timestamp traded_at "Execution timestamp"
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at "Soft delete"
    }

    REBATES {
        uuid id PK "Primary Key"
        uuid user_id FK "Beneficiary user (referrer)"
        uuid trade_id FK "Source trade"
        uuid referrer_id FK "User who gets the rebate"
        integer level "Commission level (1-5)"
        decimal amount "Rebate amount"
        decimal rate "Commission rate (0.01-0.1)"
        varchar status "pending, processed, failed"
        timestamp processed_at "When rebate was processed"
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at "Soft delete"
    }

    NOTIFICATIONS {
        uuid id PK "Primary Key"
        uuid user_id FK "Target user"
        varchar type "rebate, trade, system, hierarchy"
        varchar title "Notification title"
        text message "Notification content"
        varchar channel "email, telegram, webhook, push"
        varchar status "pending, sent, failed, delivered"
        timestamp sent_at "When notification was sent"
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at "Soft delete"
    }

    %% Self-referencing relationship
    USERS ||--o{ USERS : "parent_id (referral)"

    %% User relationships
    USERS ||--o{ HIERARCHIES : "user_id"
    USERS ||--o{ HIERARCHIES : "ancestor_id"
    USERS ||--o{ TRADES : "user_id"
    USERS ||--o{ REBATES : "user_id"
    USERS ||--o{ REBATES : "referrer_id"
    USERS ||--o{ NOTIFICATIONS : "user_id"

    %% Trade to Rebate relationship
    TRADES ||--o{ REBATES : "trade_id"