flowchart TD
    A[New User Registration] --> B{Has Referral Code?}
    B -->|Yes| C[Find Parent User]
    B -->|No| D[Create Root User]

    C --> E[Create User Record]
    D --> E

    E --> F[Create Self-Hierarchy Entry<br/>level=0, path=userID]
    F --> G{Has Parent?}

    G -->|Yes| H[Get Parent's Ancestors<br/>up to 5 levels]
    G -->|No| I[Registration Complete]

    H --> J[Create Hierarchy Entries<br/>for each ancestor + 1 level]
    J --> K[Update Parent's Direct Children]
    K --> I

    %% Trading Flow
    L[User Places Trade] --> M[Execute Trade]
    M --> N[Calculate Trading Fee]
    N --> O[Update User Volume]
    O --> P[Trigger Rebate Calculation]

    P --> Q[Get User's Hierarchy<br/>up to 5 levels]
    Q --> R[Calculate Commission<br/>for each level]
    R --> S[Create Rebate Records]
    S --> T[Update Referrer Stats]
    T --> U[Send Notifications]

    %% Commission Rates
    V[Commission Calculation] --> W[Level 1: 10%]
    V --> X[Level 2: 5%]
    V --> Y[Level 3: 3%]
    V --> Z[Level 4: 2%]
    V --> AA[Level 5: 1%]