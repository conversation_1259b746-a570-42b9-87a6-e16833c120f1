package model

import "github.com/google/uuid"

type Hierarchy struct {
	BaseModel
	UserID     uuid.UUID `gorm:"column:user_id;type:uuid;not null"`
	AncestorID uuid.UUID `gorm:"column:ancestor_id;type:uuid;not null"`
	Level      int       `gorm:"column:level;not null"`
	Path       string    `gorm:"column:path;size:1000;not null"` // Path from root to this node
	IsDirect   bool      `gorm:"column:is_direct;default:false"` // Direct parent-child relationship

	// Relationships
	User     User `gorm:"foreignKey:UserID"`
	Ancestor User `gorm:"foreignKey:AncestorID"`
}

func (Hierarchy) TableName() string {
	return "hierarchies"
}
