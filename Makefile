.PHONY: build run test clean docker-build docker-run migrate

# Build the application
build:
	go build -o bin/xbit-agent ./cmd/app

# Run the application
run:
	go run ./cmd/app

# Run tests
test:
	go test ./...

# Clean build artifacts
clean:
	rm -rf bin/

# Build Docker image
docker-build:
	docker build -t xbit-agent .

# Run with Docker Compose
docker-run:
	docker-compose up -d

# Stop Docker Compose
docker-stop:
	docker-compose down

# Generate database migration
migrate:
	atlas migrate diff --env gorm

# Apply database migration
migrate-apply:
	atlas migrate apply --env gorm

# Generate GraphQL code
generate:
	go run github.com/99designs/gqlgen generate

# Generate schema from GORM models
schema:
	go run ./cmd/atlasloader

# Install dependencies
deps:
	go mod download
	go mod tidy

# Format code
fmt:
	go fmt ./...

# Lint code
lint:
	golangci-lint run

# Run all checks
check: fmt lint test

# Development setup
dev-setup: deps generate build
