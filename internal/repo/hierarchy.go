package repo

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type HierarchyRepo interface {
	Create(ctx context.Context, hierarchy *model.Hierarchy) error
	GetAncestors(ctx context.Context, userID string, maxLevel int) ([]*model.Hierarchy, error)
	GetDescendants(ctx context.Context, userID string, maxLevel int) ([]*model.Hierarchy, error)
	GetDirectChildren(ctx context.Context, userID string) ([]*model.Hierarchy, error)
	UpdatePath(ctx context.Context, userID string, path string) error
	DeleteByUserID(ctx context.Context, userID string) error
}

type hierarchyRepo struct {
	db *gorm.DB
}

func NewHierarchyRepo(db *gorm.DB) HierarchyRepo {
	return &hierarchyRepo{db: db}
}

func (r *hierarchyRepo) Create(ctx context.Context, hierarchy *model.Hierarchy) error {
	return r.db.WithContext(ctx).Create(hierarchy).Error
}

func (r *hierarchyRepo) GetAncestors(ctx context.Context, userID string, maxLevel int) ([]*model.Hierarchy, error) {
	var hierarchies []*model.Hierarchy
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND level <= ?", userID, maxLevel).
		Order("level ASC").
		Find(&hierarchies).Error
	return hierarchies, err
}

func (r *hierarchyRepo) GetDescendants(ctx context.Context, userID string, maxLevel int) ([]*model.Hierarchy, error) {
	var hierarchies []*model.Hierarchy
	err := r.db.WithContext(ctx).
		Where("ancestor_id = ? AND level <= ?", userID, maxLevel).
		Order("level ASC").
		Find(&hierarchies).Error
	return hierarchies, err
}

func (r *hierarchyRepo) GetDirectChildren(ctx context.Context, userID string) ([]*model.Hierarchy, error) {
	var hierarchies []*model.Hierarchy
	err := r.db.WithContext(ctx).
		Where("ancestor_id = ? AND level = 1", userID).
		Find(&hierarchies).Error
	return hierarchies, err
}

func (r *hierarchyRepo) UpdatePath(ctx context.Context, userID string, path string) error {
	return r.db.WithContext(ctx).
		Model(&model.Hierarchy{}).
		Where("user_id = ?", userID).
		Update("path", path).Error
}

func (r *hierarchyRepo) DeleteByUserID(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&model.Hierarchy{}).Error
}
