package model

import (
	"time"

	"github.com/google/uuid"
)

type Notification struct {
	BaseModel
	UserID  uuid.UUID  `gorm:"column:user_id;type:uuid;not null"`
	Type    string     `gorm:"column:type;size:50;not null"` // rebate, trade, system
	Title   string     `gorm:"column:title;size:255;not null"`
	Message string     `gorm:"column:message;type:text;not null"`
	Channel string     `gorm:"column:channel;size:20;not null"` // email, telegram, webhook
	Status  string     `gorm:"column:status;size:20;default:'pending'"`
	SentAt  *time.Time `gorm:"column:sent_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID"`
}

func (Notification) TableName() string {
	return "notifications"
}
