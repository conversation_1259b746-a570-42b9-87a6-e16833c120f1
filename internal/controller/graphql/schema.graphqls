directive @auth on FIELD_DEFINITION

type Query {
  # Health check
  health: HealthResponse!

  # User queries
  getUser(id: ID!): UserResponse! @auth
  getUserByWallet(walletAddress: String!): UserResponse!
  getUserHierarchy(userID: ID!, maxLevel: Int): HierarchyResponse! @auth
  getUserDescendants(userID: ID!, maxLevel: Int): UserListResponse! @auth

  # Rebate queries
  getUserRebates(userID: ID!): RebateListResponse! @auth
  getPendingRebates: RebateListResponse! @auth

  # Trade queries
  getUserTrades(userID: ID!): TradeListResponse! @auth
}

type Mutation {
  # User mutations
  createUser(input: CreateUserRequest!): UserResponse!
  updateUser(input: UpdateUserRequest!): UserResponse! @auth

  # Hierarchy mutations
  createHierarchy(input: CreateHierarchyRequest!): HierarchyResponse! @auth
  updateHierarchy(input: UpdateHierarchyRequest!): HierarchyResponse! @auth

  # Rebate mutations
  calculateRebate(input: CalculateRebateRequest!): RebateResponse! @auth
  processRebates: ProcessRebatesResponse! @auth

  # Trade mutations
  createTrade(input: CreateTradeRequest!): TradeResponse! @auth
}

# Response types
type HealthResponse {
  status: String!
  timestamp: Time!
}

type UserResponse {
  success: Boolean!
  message: String
  user: User
}

type UserListResponse {
  success: Boolean!
  message: String
  users: [User!]!
  total: Int!
}

type HierarchyResponse {
  success: Boolean!
  message: String
  hierarchy: [User!]!
  levels: Int!
}

type RebateResponse {
  success: Boolean!
  message: String
  rebate: Rebate
}

type RebateListResponse {
  success: Boolean!
  message: String
  rebates: [Rebate!]!
  total: Int!
}

type ProcessRebatesResponse {
  success: Boolean!
  message: String
  processed: Int!
}

type TradeResponse {
  success: Boolean!
  message: String
  trade: Trade
}

type TradeListResponse {
  success: Boolean!
  message: String
  trades: [Trade!]!
  total: Int!
}

# Input types
input CreateUserRequest {
  username: String!
  email: String!
  walletAddress: String!
  referralCode: String
  parentID: ID
}

input UpdateUserRequest {
  id: ID!
  username: String
  email: String
  walletAddress: String
  status: String
}

input CreateHierarchyRequest {
  userID: ID!
  parentID: ID
}

input UpdateHierarchyRequest {
  userID: ID!
  newParentID: ID
}

input CalculateRebateRequest {
  userID: ID!
  tradeVolume: Float!
}

input CreateTradeRequest {
  userID: ID!
  symbol: String!
  side: String!
  amount: Float!
  price: Float!
  volume: Float!
  fee: Float!
}