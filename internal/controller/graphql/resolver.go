package graphql

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	UserResolver      *resolvers.UserResolver
	RebateResolver    *resolvers.RebateResolver
	HierarchyResolver *resolvers.HierarchyResolver
	TradeResolver     *resolvers.TradeResolver
}

func NewRootResolver() *Resolver {
	return &Resolver{
		UserResolver:      resolvers.NewUserResolver(),
		RebateResolver:    resolvers.NewRebateResolver(),
		HierarchyResolver: resolvers.NewHierarchyResolver(),
		TradeResolver:     resolvers.NewTradeResolver(),
	}
}
