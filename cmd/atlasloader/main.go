package main

import (
	"context"
	"log"
	"os"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"

	"ariga.io/atlas-provider-gorm/gormschema"
)

func main() {
	ctx := context.Background()

	// Create a gorm schema loader
	loader, err := gormschema.NewLoader("postgres")
	if err != nil {
		log.Fatalf("failed to create schema loader: %v", err)
	}

	// Load schema from GORM models
	schema, err := loader.Load(ctx, []interface{}{
		&model.User{},
		&model.Rebate{},
		&model.Trade{},
		&model.Notification{},
		&model.Hierarchy{},
	}...)
	if err != nil {
		log.Fatalf("failed to load schema: %v", err)
	}

	// Print the schema
	sql, err := schema.MarshalText()
	if err != nil {
		log.Fatalf("failed to marshal schema: %v", err)
	}

	os.Stdout.Write(sql)
}
