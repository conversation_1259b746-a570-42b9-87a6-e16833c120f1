package initializer

import (
	"context"
	"net/http"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

type App struct {
	server   *http.Server
	logger   *zap.Logger
	services *service.Services
}

func Initialize() (*App, error) {
	// Initialize logger
	logger, err := global.InitializeLogger()
	if err != nil {
		return nil, err
	}

	// Initialize database
	db, err := global.InitializeDatabase()
	if err != nil {
		return nil, err
	}

	// Initialize repositories
	userRepo := repo.NewUserRepo(db)
	rebateRepo := repo.NewRebateRepo(db)
	hierarchyRepo := repo.NewHierarchyRepo(db)

	// Initialize services
	commissionRates := map[int]float64{
		1: 0.1,  // 10%
		2: 0.05, // 5%
		3: 0.03, // 3%
		4: 0.02, // 2%
		5: 0.01, // 1%
	}

	rebateService := service.NewRebateService(userRepo, rebateRepo, hierarchyRepo, logger, commissionRates, 5)
	hierarchyService := service.NewHierarchyService(userRepo, hierarchyRepo, logger, 5)

	services := &service.Services{
		RebateService:    rebateService,
		HierarchyService: hierarchyService,
	}

	// Initialize router
	router := gin.Default()
	controller.SetupRoutes(router, services, logger)

	// Create HTTP server
	server := &http.Server{
		Addr:    ":9001",
		Handler: router,
	}

	return &App{
		server:   server,
		logger:   logger,
		services: services,
	}, nil
}

func (app *App) Start(ctx context.Context) error {
	app.logger.Info("Starting Xbit Agent server", zap.String("addr", app.server.Addr))

	// Start server in a goroutine
	go func() {
		if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.logger.Error("Server error", zap.Error(err))
		}
	}()

	// Wait for context cancellation
	<-ctx.Done()

	// Graceful shutdown
	app.logger.Info("Shutting down server...")
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := app.server.Shutdown(shutdownCtx); err != nil {
		app.logger.Error("Server shutdown error", zap.Error(err))
		return err
	}

	app.logger.Info("Server stopped gracefully")
	return nil
}
