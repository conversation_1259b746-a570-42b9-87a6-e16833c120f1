graph LR
    A[Performance Optimization] --> B[Primary Indexes]
    A --> C[Foreign Key Indexes]
    A --> D[Composite Indexes]
    A --> E[Unique Constraints]

    B --> B1[users.id]
    B --> B2[trades.id]
    B --> B3[rebates.id]
    B --> B4[hierarchies.id]
    B --> B5[notifications.id]

    C --> C1[users.parent_id]
    C --> C2[hierarchies.user_id]
    C --> C3[hierarchies.ancestor_id]
    C --> C4[trades.user_id]
    C --> C5[rebates.user_id]
    C --> C6[rebates.trade_id]
    C --> C7[notifications.user_id]

    D --> D1[hierarchies(user_id, level)]
    D --> D2[rebates(status, created_at)]
    D --> D3[trades(user_id, traded_at)]
    D --> D4[notifications(status, channel)]

    E --> E1[users.username]
    E --> E2[users.email]
    E --> E3[users.referral_code]
    E --> E4[hierarchies(user_id, ancestor_id)]