package resolvers

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"go.uber.org/zap"
)

type UserResolver struct {
	userService *service.UserService
	logger      *zap.Logger
}

func NewUserResolver() *UserResolver {
	return &UserResolver{}
}

func (r *UserResolver) GetUser(ctx context.Context, id string) (*model.User, error) {
	// TODO: Implement get user by ID
	return nil, nil
}

func (r *UserResolver) GetUserByWallet(ctx context.Context, walletAddress string) (*model.User, error) {
	// TODO: Implement get user by wallet address
	return nil, nil
}

func (r *UserResolver) CreateUser(ctx context.Context, input model.CreateUserRequest) (*model.User, error) {
	// TODO: Implement create user
	return nil, nil
}

func (r *UserResolver) UpdateUser(ctx context.Context, input model.UpdateUserRequest) (*model.User, error) {
	// TODO: Implement update user
	return nil, nil
}
