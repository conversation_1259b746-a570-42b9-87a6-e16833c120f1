package service

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"go.uber.org/zap"
)

type HierarchyService interface {
	CreateUserHierarchy(ctx context.Context, userID string, parentID *string) error
	GetUserHierarchy(ctx context.Context, userID string, maxLevel int) ([]*model.User, error)
	GetUserDescendants(ctx context.Context, userID string, maxLevel int) ([]*model.User, error)
	UpdateHierarchy(ctx context.Context, userID string, newParentID *string) error
	DeleteUserHierarchy(ctx context.Context, userID string) error
}

type hierarchyService struct {
	userRepo      repo.UserRepo
	hierarchyRepo repo.HierarchyRepo
	logger        *zap.Logger
	maxLevels     int
}

func NewHierarchyService(
	userRepo repo.UserRepo,
	hierarchyRepo repo.HierarchyRepo,
	logger *zap.Logger,
	maxLevels int,
) HierarchyService {
	return &hierarchyService{
		userRepo:      userRepo,
		hierarchyRepo: hierarchyRepo,
		logger:        logger,
		maxLevels:     maxLevels,
	}
}

func (s *hierarchyService) CreateUserHierarchy(ctx context.Context, userID string, parentID *string) error {
	s.logger.Info("Creating user hierarchy",
		zap.String("userID", userID),
		zap.Any("parentID", parentID))

	// Create self-reference
	selfHierarchy := &model.Hierarchy{
		UserID:     userID,
		AncestorID: userID,
		Level:      0,
		Path:       userID,
		IsDirect:   true,
	}

	if err := s.hierarchyRepo.Create(ctx, selfHierarchy); err != nil {
		s.logger.Error("Failed to create self hierarchy", zap.Error(err))
		return err
	}

	// If no parent, we're done
	if parentID == nil {
		return nil
	}

	// Get parent's hierarchy
	parentAncestors, err := s.hierarchyRepo.GetAncestors(ctx, *parentID, s.maxLevels)
	if err != nil {
		s.logger.Error("Failed to get parent ancestors", zap.Error(err))
		return err
	}

	// Create hierarchy entries for each ancestor
	for _, ancestor := range parentAncestors {
		if ancestor.Level >= s.maxLevels {
			continue // Skip if we've reached max levels
		}

		newLevel := ancestor.Level + 1
		newPath := fmt.Sprintf("%s/%s", ancestor.Path, userID)

		hierarchy := &model.Hierarchy{
			UserID:     userID,
			AncestorID: ancestor.AncestorID,
			Level:      newLevel,
			Path:       newPath,
			IsDirect:   newLevel == 1, // Direct parent-child relationship
		}

		if err := s.hierarchyRepo.Create(ctx, hierarchy); err != nil {
			s.logger.Error("Failed to create hierarchy entry", zap.Error(err))
			return err
		}
	}

	// Create direct parent-child relationship
	directHierarchy := &model.Hierarchy{
		UserID:     userID,
		AncestorID: *parentID,
		Level:      1,
		Path:       fmt.Sprintf("%s/%s", *parentID, userID),
		IsDirect:   true,
	}

	if err := s.hierarchyRepo.Create(ctx, directHierarchy); err != nil {
		s.logger.Error("Failed to create direct hierarchy", zap.Error(err))
		return err
	}

	s.logger.Info("Successfully created user hierarchy", zap.String("userID", userID))
	return nil
}

func (s *hierarchyService) GetUserHierarchy(ctx context.Context, userID string, maxLevel int) ([]*model.User, error) {
	return s.userRepo.GetHierarchy(ctx, userID, maxLevel)
}

func (s *hierarchyService) GetUserDescendants(ctx context.Context, userID string, maxLevel int) ([]*model.User, error) {
	descendants, err := s.hierarchyRepo.GetDescendants(ctx, userID, maxLevel)
	if err != nil {
		return nil, err
	}

	var users []*model.User
	for _, descendant := range descendants {
		user, err := s.userRepo.GetByID(ctx, descendant.UserID.String())
		if err != nil {
			s.logger.Error("Failed to get descendant user", zap.Error(err))
			continue
		}
		users = append(users, user)
	}

	return users, nil
}

func (s *hierarchyService) UpdateHierarchy(ctx context.Context, userID string, newParentID *string) error {
	s.logger.Info("Updating user hierarchy",
		zap.String("userID", userID),
		zap.Any("newParentID", newParentID))

	// Delete existing hierarchy
	if err := s.hierarchyRepo.DeleteByUserID(ctx, userID); err != nil {
		s.logger.Error("Failed to delete existing hierarchy", zap.Error(err))
		return err
	}

	// Recreate hierarchy with new parent
	return s.CreateUserHierarchy(ctx, userID, newParentID)
}

func (s *hierarchyService) DeleteUserHierarchy(ctx context.Context, userID string) error {
	s.logger.Info("Deleting user hierarchy", zap.String("userID", userID))
	return s.hierarchyRepo.DeleteByUserID(ctx, userID)
}
