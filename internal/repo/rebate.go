package repo

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type RebateRepo interface {
	Create(ctx context.Context, rebate *model.Rebate) error
	GetByID(ctx context.Context, id string) (*model.Rebate, error)
	GetByUserID(ctx context.Context, userID string) ([]*model.Rebate, error)
	GetPendingRebates(ctx context.Context) ([]*model.Rebate, error)
	UpdateStatus(ctx context.Context, id string, status string) error
	GetByReferrerID(ctx context.Context, referrerID string) ([]*model.Rebate, error)
}

type rebateRepo struct {
	db *gorm.DB
}

func NewRebateRepo(db *gorm.DB) RebateRepo {
	return &rebateRepo{db: db}
}

func (r *rebateRepo) Create(ctx context.Context, rebate *model.Rebate) error {
	return r.db.WithContext(ctx).Create(rebate).Error
}

func (r *rebateRepo) GetByID(ctx context.Context, id string) (*model.Rebate, error) {
	var rebate model.Rebate
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&rebate).Error
	if err != nil {
		return nil, err
	}
	return &rebate, nil
}

func (r *rebateRepo) GetByUserID(ctx context.Context, userID string) ([]*model.Rebate, error) {
	var rebates []*model.Rebate
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&rebates).Error
	return rebates, err
}

func (r *rebateRepo) GetPendingRebates(ctx context.Context) ([]*model.Rebate, error) {
	var rebates []*model.Rebate
	err := r.db.WithContext(ctx).Where("status = ?", "pending").Find(&rebates).Error
	return rebates, err
}

func (r *rebateRepo) UpdateStatus(ctx context.Context, id string, status string) error {
	return r.db.WithContext(ctx).
		Model(&model.Rebate{}).
		Where("id = ?", id).
		Update("status", status).Error
}

func (r *rebateRepo) GetByReferrerID(ctx context.Context, referrerID string) ([]*model.Rebate, error) {
	var rebates []*model.Rebate
	err := r.db.WithContext(ctx).Where("referrer_id = ?", referrerID).Find(&rebates).Error
	return rebates, err
}
