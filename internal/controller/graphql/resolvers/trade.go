package resolvers

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"go.uber.org/zap"
)

type TradeResolver struct {
	tradeService *service.TradeService
	logger       *zap.Logger
}

func NewTradeResolver() *TradeResolver {
	return &TradeResolver{}
}

func (r *TradeResolver) GetUserTrades(ctx context.Context, userID string) ([]*model.Trade, error) {
	// TODO: Implement get user trades
	return nil, nil
}

func (r *TradeResolver) CreateTrade(ctx context.Context, userID string, symbol string, side string, amount float64, price float64, volume float64, fee float64) (*model.Trade, error) {
	// TODO: Implement create trade
	return nil, nil
}
