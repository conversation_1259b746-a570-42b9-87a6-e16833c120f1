package resolvers

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"go.uber.org/zap"
)

type HierarchyResolver struct {
	hierarchyService *service.HierarchyService
	logger           *zap.Logger
}

func NewHierarchyResolver() *HierarchyResolver {
	return &HierarchyResolver{}
}

func (r *HierarchyResolver) GetUserHierarchy(ctx context.Context, userID string, maxLevel *int) ([]*model.User, error) {
	// TODO: Implement get user hierarchy
	return nil, nil
}

func (r *HierarchyResolver) GetUserDescendants(ctx context.Context, userID string, maxLevel *int) ([]*model.User, error) {
	// TODO: Implement get user descendants
	return nil, nil
}

func (r *HierarchyResolver) CreateHierarchy(ctx context.Context, userID string, parentID *string) ([]*model.User, error) {
	// TODO: Implement create hierarchy
	return nil, nil
}

func (r *HierarchyResolver) UpdateHierarchy(ctx context.Context, userID string, newParentID *string) ([]*model.User, error) {
	// TODO: Implement update hierarchy
	return nil, nil
}
