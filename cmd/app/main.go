package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
)

func main() {
	// Initialize the application
	app, err := initializer.Initialize()
	if err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// Start the application
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sig<PERSON>han
		log.Println("Received shutdown signal, gracefully shutting down...")
		cancel()
	}()

	// Start the application
	if err := app.Start(ctx); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}
}
