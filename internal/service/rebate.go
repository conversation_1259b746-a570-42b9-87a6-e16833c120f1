package service

import (
	"context"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"go.uber.org/zap"
)

type RebateService interface {
	ProcessRebates(ctx context.Context) error
	CalculateRebate(ctx context.Context, userID string, tradeVolume float64) error
	GetUserRebates(ctx context.Context, userID string) ([]*model.Rebate, error)
	GetPendingRebates(ctx context.Context) ([]*model.Rebate, error)
}

type rebateService struct {
	userRepo        repo.UserRepo
	rebateRepo      repo.RebateRepo
	hierarchyRepo   repo.HierarchyRepo
	logger          *zap.Logger
	commissionRates map[int]float64
	maxLevels       int
}

func NewRebateService(
	userRepo repo.UserRepo,
	rebateRepo repo.RebateRepo,
	hierarchyRepo repo.HierarchyRepo,
	logger *zap.Logger,
	commissionRates map[int]float64,
	maxLevels int,
) RebateService {
	return &rebateService{
		userRepo:        userRepo,
		rebateRepo:      rebateRepo,
		hierarchyRepo:   hierarchyRepo,
		logger:          logger,
		commissionRates: commissionRates,
		maxLevels:       maxLevels,
	}
}

func (s *rebateService) ProcessRebates(ctx context.Context) error {
	s.logger.Info("Starting rebate processing")

	pendingRebates, err := s.rebateRepo.GetPendingRebates(ctx)
	if err != nil {
		s.logger.Error("Failed to get pending rebates", zap.Error(err))
		return err
	}

	for _, rebate := range pendingRebates {
		if err := s.processRebate(ctx, rebate); err != nil {
			s.logger.Error("Failed to process rebate",
				zap.String("rebateID", rebate.ID.String()),
				zap.Error(err))
			continue
		}
	}

	s.logger.Info("Completed rebate processing", zap.Int("processed", len(pendingRebates)))
	return nil
}

func (s *rebateService) CalculateRebate(ctx context.Context, userID string, tradeVolume float64) error {
	s.logger.Info("Calculating rebate",
		zap.String("userID", userID),
		zap.Float64("volume", tradeVolume))

	// Get user's hierarchy (ancestors)
	ancestors, err := s.hierarchyRepo.GetAncestors(ctx, userID, s.maxLevels)
	if err != nil {
		s.logger.Error("Failed to get user hierarchy", zap.Error(err))
		return err
	}

	// Calculate rebate for each level
	for _, ancestor := range ancestors {
		rate, exists := s.commissionRates[ancestor.Level]
		if !exists {
			s.logger.Warn("No commission rate found for level", zap.Int("level", ancestor.Level))
			continue
		}

		commission := tradeVolume * rate

		rebate := &model.Rebate{
			UserID:      ancestor.UserID,
			ReferrerID:  ancestor.AncestorID,
			Level:       ancestor.Level,
			Amount:      commission,
			Commission:  rate,
			TradeVolume: tradeVolume,
			Status:      "pending",
		}

		if err := s.rebateRepo.Create(ctx, rebate); err != nil {
			s.logger.Error("Failed to create rebate record", zap.Error(err))
			return err
		}

		s.logger.Info("Created rebate record",
			zap.String("userID", userID),
			zap.String("referrerID", ancestor.AncestorID.String()),
			zap.Int("level", ancestor.Level),
			zap.Float64("commission", commission))
	}

	// Update user's total volume
	if err := s.userRepo.UpdateVolume(ctx, userID, tradeVolume); err != nil {
		s.logger.Error("Failed to update user volume", zap.Error(err))
		return err
	}

	return nil
}

func (s *rebateService) GetUserRebates(ctx context.Context, userID string) ([]*model.Rebate, error) {
	return s.rebateRepo.GetByUserID(ctx, userID)
}

func (s *rebateService) GetPendingRebates(ctx context.Context) ([]*model.Rebate, error) {
	return s.rebateRepo.GetPendingRebates(ctx)
}

func (s *rebateService) processRebate(ctx context.Context, rebate *model.Rebate) error {
	// Here you would implement the actual rebate processing logic
	// For example, transferring funds, updating balances, etc.

	// For now, just mark as processed
	now := time.Now()
	rebate.Status = "processed"
	rebate.ProcessedAt = &now

	if err := s.rebateRepo.UpdateStatus(ctx, rebate.ID.String(), "processed"); err != nil {
		return err
	}

	// Update referrer's total rebate
	if err := s.userRepo.UpdateRebate(ctx, rebate.ReferrerID.String(), rebate.Amount); err != nil {
		s.logger.Error("Failed to update referrer rebate", zap.Error(err))
		return err
	}

	s.logger.Info("Processed rebate",
		zap.String("rebateID", rebate.ID.String()),
		zap.Float64("amount", rebate.Amount))

	return nil
}
