package model

import "github.com/google/uuid"

type User struct {
	BaseModel
	Username      string     `gorm:"column:username;size:255;not null;unique"`
	Email         string     `gorm:"column:email;size:255;not null;unique"`
	WalletAddress string     `gorm:"column:wallet_address;size:255;unique"`
	ReferralCode  string     `gorm:"column:referral_code;size:50;unique"`
	ParentID      *uuid.UUID `gorm:"column:parent_id;type:uuid"`
	Level         int        `gorm:"column:level;default:0"`
	Status        string     `gorm:"column:status;size:20;default:'active'"`
	TotalVolume   float64    `gorm:"column:total_volume;default:0"`
	TotalRebate   float64    `gorm:"column:total_rebate;default:0"`

	// Relationships
	Parent   *User    `gorm:"foreignKey:ParentID"`
	Children []User   `gorm:"foreignKey:ParentID"`
	Rebates  []Rebate `gorm:"foreignKey:UserID"`
}

func (User) TableName() string {
	return "users"
}
