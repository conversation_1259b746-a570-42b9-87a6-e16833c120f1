graph TD
    A[Root User A<br/>Level 0] --> B[User B<br/>Level 1<br/>10% commission]
    A --> C[User C<br/>Level 1<br/>10% commission]

    B --> D[User D<br/>Level 2<br/>5% commission]
    B --> E[User E<br/>Level 2<br/>5% commission]

    D --> F[User F<br/>Level 3<br/>3% commission]
    E --> G[User G<br/>Level 3<br/>3% commission]

    F --> H[User H<br/>Level 4<br/>2% commission]
    G --> I[User I<br/>Level 4<br/>2% commission]

    H --> J[User J<br/>Level 5<br/>1% commission]

    %% When User J trades $1000 with $10 fee
    J -.->|Trade: $1000, Fee: $10| K[Commission Distribution]
    K --> L[User H gets: $10 × 2% = $0.20]
    K --> M[User G gets: $10 × 3% = $0.30]
    K --> N[User E gets: $10 × 5% = $0.50]
    K --> O[User B gets: $10 × 10% = $1.00]
    K --> P[User A gets: $10 × 10% = $1.00]