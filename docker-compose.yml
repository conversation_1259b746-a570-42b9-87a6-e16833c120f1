version: '3.8'

services:
  xbit-agent:
    build: .
    ports:
      - "9001:9001"
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASS=password
      - POSTGRES_DB=xbit_agent
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASS=
      - NATS_URL=nats://nats:4222
      - NATS_AUTH_TOKEN=
    depends_on:
      - postgres
      - redis
      - nats
    volumes:
      - ./config.yaml:/root/config.yaml
    restart: unless-stopped

  postgres:
    image: postgres:14.1
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=xbit_agent
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nats:
    image: nats:2-alpine
    ports:
      - "4222:4222"
      - "8222:8222"
    command: -js -m 8222
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data: