package repo

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type UserRepo interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, id string) (*model.User, error)
	GetByWalletAddress(ctx context.Context, walletAddress string) (*model.User, error)
	GetByReferralCode(ctx context.Context, referralCode string) (*model.User, error)
	GetHierarchy(ctx context.Context, userID string, maxLevel int) ([]*model.User, error)
	GetChildren(ctx context.Context, userID string) ([]*model.User, error)
	Update(ctx context.Context, user *model.User) error
	UpdateVolume(ctx context.Context, userID string, volume float64) error
	UpdateRebate(ctx context.Context, userID string, rebate float64) error
}

type userRepo struct {
	db *gorm.DB
}

func NewUserRepo(db *gorm.DB) UserRepo {
	return &userRepo{db: db}
}

func (r *userRepo) Create(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepo) GetByID(ctx context.Context, id string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepo) GetByWalletAddress(ctx context.Context, walletAddress string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("wallet_address = ?", walletAddress).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepo) GetByReferralCode(ctx context.Context, referralCode string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("referral_code = ?", referralCode).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepo) GetHierarchy(ctx context.Context, userID string, maxLevel int) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).
		Joins("JOIN hierarchies h ON users.id = h.ancestor_id").
		Where("h.user_id = ? AND h.level <= ?", userID, maxLevel).
		Order("h.level ASC").
		Find(&users).Error
	return users, err
}

func (r *userRepo) GetChildren(ctx context.Context, userID string) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).
		Where("parent_id = ?", userID).
		Find(&users).Error
	return users, err
}

func (r *userRepo) Update(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

func (r *userRepo) UpdateVolume(ctx context.Context, userID string, volume float64) error {
	return r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		UpdateColumn("total_volume", gorm.Expr("total_volume + ?", volume)).
		Error
}

func (r *userRepo) UpdateRebate(ctx context.Context, userID string, rebate float64) error {
	return r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		UpdateColumn("total_rebate", gorm.Expr("total_rebate + ?", rebate)).
		Error
}
