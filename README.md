# Xbit Agent

Xbit Agent là một hệ thống agent modular được thiết kế để xử lý các tác vụ tự động trong hệ sinh thái Xbit DEX, bao gồm quản lý rebate, hierarchy và notification.

## Tính năng chính

### 🔄 Rebate Agent
- T<PERSON>h toán và xử lý rebate tự động
- Hỗ trợ multi-level commission (tối đa 5 cấp)
- Tích hợp với hệ thống giao dịch
- Theo dõi và báo cáo rebate real-time

### 🌳 Hierarchy Management
- Quản lý cấu trúc phân cấp nhiều cấp
- Hỗ trợ referral system
- Tự động cập nhật hierarchy khi có thay đổi
- API để truy vấn và quản lý hierarchy

### 📢 Notification Agent
- G<PERSON>i thông báo qua nhiều kênh (email, telegram, webhook)
- T<PERSON><PERSON> hợ<PERSON> với các sự kiện hệ thống
- Hỗ trợ template và personalization

## Cấu trúc dự án

```
xbit-agent/
├── cmd/
│   ├── app/                 # Main application entry point
│   └── atlasloader/         # Database schema loader
├── internal/
│   ├── controller/          # HTTP controllers
│   ├── model/              # Database models
│   ├── repo/               # Repository layer
│   ├── service/            # Business logic layer
│   ├── global/             # Global configurations
│   └── initializer/        # Application initializer
├── migrations/             # Database migrations
├── config.yaml             # Configuration file
├── atlas.hcl              # Atlas configuration
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Development environment
└── Makefile               # Build and development commands
```

## Database Schema

### Users Table
- Quản lý thông tin người dùng
- Hỗ trợ referral system
- Tracking volume và rebate

### Hierarchies Table
- Lưu trữ cấu trúc phân cấp nhiều cấp
- Sử dụng closure table pattern
- Hỗ trợ truy vấn hiệu quả

### Rebates Table
- Lưu trữ thông tin rebate
- Tracking status và processing time
- Liên kết với user hierarchy

### Trades Table
- Lưu trữ thông tin giao dịch
- Tracking volume và fees
- Trigger rebate calculation

### Notifications Table
- Lưu trữ thông báo
- Tracking delivery status
- Hỗ trợ multiple channels

## Cài đặt và chạy

### Yêu cầu hệ thống
- Go 1.23.7+
- PostgreSQL 14+
- Redis 7+
- NATS 2+

### Development Setup

1. Clone repository:
```bash
git clone https://gitlab.ggwp.life/xbit/xbit-dex/xbit-agent.git
cd xbit-agent
```

2. Cài đặt dependencies:
```bash
make deps
```

3. Cấu hình environment:
```bash
cp config.yaml.example config.yaml
# Chỉnh sửa config.yaml theo môi trường
```

4. Chạy với Docker Compose:
```bash
make docker-run
```

5. Hoặc chạy locally:
```bash
make build
make run
```

### Production Deployment

1. Build Docker image:
```bash
make docker-build
```

2. Deploy với Docker Compose:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## API Endpoints

### GraphQL API
- `POST /graphql` - GraphQL endpoint chính
- `GET /playground` - GraphQL playground để test queries

### GraphQL Queries
```graphql
# Health check
query {
  health {
    status
    timestamp
  }
}

# Get user
query {
  getUser(id: "user-id") {
    success
    message
    user {
      id
      username
      email
      walletAddress
      totalVolume
      totalRebate
    }
  }
}

# Get user hierarchy
query {
  getUserHierarchy(userID: "user-id", maxLevel: 5) {
    success
    message
    hierarchy {
      id
      username
      level
    }
    levels
  }
}

# Get user rebates
query {
  getUserRebates(userID: "user-id") {
    success
    message
    rebates {
      id
      amount
      commission
      level
      status
    }
    total
  }
}
```

### GraphQL Mutations
```graphql
# Create user
mutation {
  createUser(input: {
    username: "testuser"
    email: "<EMAIL>"
    walletAddress: "0x123..."
    referralCode: "REF123"
  }) {
    success
    message
    user {
      id
      username
    }
  }
}

# Calculate rebate
mutation {
  calculateRebate(input: {
    userID: "user-id"
    tradeVolume: 1000.0
  }) {
    success
    message
    rebate {
      id
      amount
      commission
    }
  }
}
```

### REST API (Simple)
- `GET /health` - Kiểm tra trạng thái hệ thống
- `GET /api/agent/health` - Health check cho agent
- `GET /api/agent/status` - Trạng thái hệ thống

## Configuration

### Database Configuration
```yaml
pgsql:
  path: localhost
  port: 5432
  db-name: xbit_agent
  username: postgres
  password: password
```

### Agent Configuration
```yaml
agent:
  rebate:
    enabled: true
    check-interval: "5m"
    max-levels: 5
    commission-rates:
      level1: 0.1  # 10%
      level2: 0.05 # 5%
      level3: 0.03 # 3%
      level4: 0.02 # 2%
      level5: 0.01 # 1%
```

## Development

### GraphQL Generation
```bash
# Generate GraphQL code
make generate
```

### Database Migration
```bash
# Generate migration
make migrate

# Apply migration
make migrate-apply
```

### Code Quality
```bash
# Format code
make fmt

# Lint code
make lint

# Run tests
make test

# Run all checks
make check
```

## Monitoring và Logging

- Structured logging với Zap
- Health check endpoints
- Metrics với Prometheus
- Distributed tracing với OpenTelemetry

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## License

MIT License - see LICENSE file for details
