package controller

import (
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"go.uber.org/zap"
)

func SetupRoutes(router *gin.Engine, services *service.Services, logger *zap.Logger) {
	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// GraphQL handler
	resolver := graphql.NewRootResolver()
	srv := handler.NewDefaultServer(graphql.NewExecutableSchema(graphql.Config{Resolvers: resolver}))

	// GraphQL endpoint
	router.POST("/graphql", func(c *gin.Context) {
		srv.ServeHTTP(c.Writer, c.Request)
	})

	// GraphQL playground
	router.GET("/playground", func(c *gin.Context) {
		playground.Handler("GraphQL", "/graphql").ServeHTTP(c.Writer, c.Request)
	})

	// Simple REST API endpoints (kept for compatibility)
	api := router.Group("/api/agent")
	{
		// Health check
		api.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		})

		// Simple endpoints for basic operations
		api.GET("/status", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "running"})
		})
	}

	logger.Info("GraphQL and REST routes setup completed")
}
